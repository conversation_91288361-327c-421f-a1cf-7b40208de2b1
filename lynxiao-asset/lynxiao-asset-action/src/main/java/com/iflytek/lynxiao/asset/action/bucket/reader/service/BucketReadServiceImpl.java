package com.iflytek.lynxiao.asset.action.bucket.reader.service;

import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.asset.action.bucket.reader.config.BucketReadProperties;
import com.iflytek.lynxiao.asset.cache.BucketCacheService;
import com.iflytek.lynxiao.asset.cache.CacheMongoTemplate;
import com.iflytek.lynxiao.asset.util.ApiResponseUtils;
import com.iflytek.lynxiao.common.component.FixedCapacityCache;
import com.iflytek.lynxiao.data.constant.AssetTaskType;
import com.iflytek.lynxiao.data.constant.Fields;
import com.iflytek.lynxiao.data.constant.IndexingErrorType;
import com.iflytek.lynxiao.data.domain.AssetAuditStatus;
import com.iflytek.lynxiao.data.domain.AssetCell;
import com.iflytek.lynxiao.data.domain.AssetCellProps;
import com.iflytek.lynxiao.data.dto.action.read.BucketReadDTO;
import com.iflytek.lynxiao.data.dto.action.read.BucketReadResponse;
import com.iflytek.lynxiao.data.dto.asset.AssetBucketStorageDTO;
import com.iflytek.lynxiao.data.dto.asset.AssetKindCode;
import com.iflytek.lynxiao.data.utils.BatchIdUtils;
import com.iflytek.lynxiao.resource.domain.AssetTaskBatch;
import com.iflytek.lynxiao.resource.repository.AssetTaskBatchRepository;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.model.Filters;
import lombok.extern.slf4j.Slf4j;
import org.bson.BsonDocument;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.slf4j.MDC;
import org.springframework.data.mongodb.core.query.BasicQuery;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.util.CollectionUtils;
import skynet.boot.mongo.DocumentCompressor;
import skynet.boot.pandora.api.*;
import skynet.boot.pandora.brave.TraceUtils;
import skynet.boot.pandora.ogma.PandoraApiRequestObserverBuilder;
import skynet.boot.pandora.support.MqSessionContext;
import skynet.boot.pandora.support.TaskCancelCache;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;

/**
 * 数据拉取
 *
 * @author: cwruan
 * @Date: 2025-05-29 16:30
 */
@Slf4j
public class BucketReadServiceImpl implements BucketReadService {

    private final BucketCacheService bucketCacheService;
    private final BucketReadProperties bucketReadProperties;
    private final PandoraApiRequestObserverBuilder apiRequestObserverBuilder;
    private final AssetTaskBatchRepository assetTaskBatchRepository;
    private final TaskCancelCache taskCancelCache;
    private final TraceUtils traceUtils;

    public BucketReadServiceImpl(PandoraApiRequestObserverBuilder apiRequestObserverBuilder, BucketCacheService bucketCacheService, BucketReadProperties bucketReadProperties,
                                 AssetTaskBatchRepository assetTaskBatchRepository, TaskCancelCache taskCancelCache, TraceUtils traceUtils) {
        this.apiRequestObserverBuilder = apiRequestObserverBuilder;
        this.bucketCacheService = bucketCacheService;
        this.bucketReadProperties = bucketReadProperties;
        this.assetTaskBatchRepository = assetTaskBatchRepository;
        this.taskCancelCache = taskCancelCache;
        this.traceUtils = traceUtils;
    }

    @Override
    public void scan(MqSessionContext sessionContext, BucketReadDTO dto, String taskId, boolean isDebug) {
        log.debug("【bucket-read-start】task={}, dto={}", taskId, dto);
        TotalCounter totalCounter = new TotalCounter(taskId, dto, bucketReadProperties.getAckUrl(), apiRequestObserverBuilder);
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        try (ExecutorService bucketReadPool = Executors.newVirtualThreadPerTaskExecutor()) {
            // 提交桶任务
            dto.getBucketCodeList().forEach(bucketContext -> futures.add(CompletableFuture.runAsync(() -> {
                if (isDebug) {
                    processDebug(sessionContext, dto, taskId, bucketContext.getCode(), totalCounter);
                } else {
                    processBucket(sessionContext, dto, taskId, bucketContext, totalCounter);
                }
            }, bucketReadPool)));
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        } catch (Exception e) {
            log.error("bucket-scan】 bucket task error", e);
            handleError(sessionContext, e);
            futures.forEach(future -> future.cancel(true));
        }
    }

    /**
     * 重试查询
     */
    @Override
    public void retry(MqSessionContext sessionContext, String taskId) {
        log.info("【bucket-read-retry-start】taskId={}", taskId);

        AtomicLong processedBatchCount = new AtomicLong(0L);
        AtomicLong totalBatchCount = new AtomicLong(0L);

        // 创建FixedCapacityCache，当缓存满时触发的回调
        Consumer<FixedCapacityCache<AssetTaskBatch>> batchCacheCallback = cache -> {
            try {
                // 避免并发修改异常，复制缓存内容
                List<AssetTaskBatch> batchesToProcess = cache.copy();

                // 使用虚拟线程池异步处理批次
                try (ExecutorService batchThreadPool = Executors.newVirtualThreadPerTaskExecutor()) {
                    List<CompletableFuture<Void>> futures = new ArrayList<>();

                    for (AssetTaskBatch taskBatch : batchesToProcess) {
                        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                            try {
                                processBatch(sessionContext, taskBatch);
                            } catch (Exception e) {
                                log.error("【bucket-read-retry-batch-error】batchId:{}, error:{}",
                                        taskBatch.getBatchId(), e.getMessage(), e);
                            } finally {
                                synchronized (processedBatchCount) {
                                    processedBatchCount.incrementAndGet();
                                    processedBatchCount.notifyAll();
                                }
                            }
                        }, batchThreadPool);
                        futures.add(future);
                    }

                    // 等待所有批次处理完成
                    CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
                }

                log.debug("【bucket-read-retry-cache-batch】taskId:{}, processed {} batches",
                        taskId, batchesToProcess.size());
            } catch (Exception e) {
                log.error("【bucket-read-retry-cache-callback】taskId:{}, error processing batch cache", taskId, e);
                handleError(sessionContext, e);
            }
        };

        // 使用配置的重试页面大小作为缓存容量
        FixedCapacityCache<AssetTaskBatch> batchCache = new FixedCapacityCache<>(
                bucketReadProperties.getRetryPageSize(), batchCacheCallback);

        try {
            // 查询失败批次并添加到缓存
            try (MongoCursor<Document> failedBatch = assetTaskBatchRepository.findByExecutionId(
                    Long.valueOf(taskId), bucketReadProperties.getRetryPageSize())) {
                while (failedBatch.hasNext()) {
                    Document doc = failedBatch.next();
                    AssetTaskBatch taskBatch = JSONObject.parseObject(JSONObject.toJSONString(doc), AssetTaskBatch.class);
                    batchCache.add(taskBatch);
                    totalBatchCount.incrementAndGet();
                }
            }

            // 刷新缓存，处理最后一批数据
            batchCache.flush();

            // 等待所有批次处理完成
            synchronized (processedBatchCount) {
                while (processedBatchCount.get() < totalBatchCount.get()) {
                    processedBatchCount.wait();
                }
            }

            log.info("【bucket-read-retry】 process finish.taskId={}, totalBatches:{}, processedBatches:{}",
                    taskId, totalBatchCount.get(), processedBatchCount.get());
        } catch (Exception e) {
            log.error("【bucket-read-retry】 taskId:{}, error:{}", taskId, e.getMessage(), e);
            handleError(sessionContext, e);
        }
    }

    private void processBucket(MqSessionContext sessionContext, BucketReadDTO dto, String taskId, BucketReadDTO.BucketContext bucketContext, TotalCounter totalCounter) {
        // 计数器
        AtomicLong count = new AtomicLong(0L);
        AtomicLong batchSize = new AtomicLong(0L);
        AtomicLong processedCount = new AtomicLong(0L);

        // 构造查询条件 调试逻辑仅ids存在条件，数据桶内的调试及未审核数据也能带到下个流程
        Bson query = dto.getType() == AssetTaskType.STATE_SYNC.getType() ? buildSyncStatusQuery(bucketContext) : buildQuery(bucketContext);
        String bucketCode = bucketContext.getCode();
        log.info("【bucket-read-query】taskId:{}, bucketCode:{}, query:{}", taskId, bucketCode, query.toBsonDocument());

        // 从缓存获取对应桶连接
        CacheMongoTemplate template = bucketCacheService.getClient(bucketCode);
        AssetBucketStorageDTO bucketStorage = this.bucketCacheService.getBucket(bucketCode);

        // 异步发送计算的count
        syncSendCount(template, bucketCode, taskId, query, totalCounter);

        // 创建FixedCapacityCache，当缓存满时触发的回调
        Consumer<FixedCapacityCache<AssetCell>> cacheCallback = cache -> {
            try {
                // 避免并发修改异常，复制缓存内容
                List<AssetCell> dataToSend = cache.copy();
                batchSize.incrementAndGet();

                // 异步发送数据
                doSendAsync(sessionContext, dataToSend, bucketStorage, null, processedCount, dataToSend.size());

                log.debug("【bucket-read-batch-sent】taskId:{}, bucketCode:{}, batchSize:{}, dataSize:{}",
                        taskId, bucketCode, batchSize.get(), dataToSend.size());
            } catch (Exception e) {
                log.error("【bucket-read-cache-callback】taskId:{}, bucketCode:{}, error sending batch", taskId, bucketCode, e);
                handleError(sessionContext, e);
            }
        };

        // 使用配置的批次大小创建缓存
        FixedCapacityCache<AssetCell> batchCache = new FixedCapacityCache<>(dto.getBatch(), cacheCallback);

        // 按照批次获取并返回
        log.info("get cursor begin. timestamp:{}", System.currentTimeMillis());
        try (MongoCursor<AssetCell> cursor = template.getCursor(query, bucketCode, dto.getBatch(), AssetCell.class, null)) {
            log.info("get cursor end. timestamp:{}", System.currentTimeMillis());
            while (cursor.hasNext()) {
                if (count.get() == 0) {
                    log.info("cursor.hasNext() first at: {}", System.currentTimeMillis());
                }
                count.incrementAndGet();

                // 每隔指定条数打印日志
                if (count.get() % bucketReadProperties.getDelayLog() == 0) {
                    log.info("【bucket-read-running】taskId={}, bucketCode={}, had read={}", taskId, bucketCode, count.get());
                }

                if (Thread.currentThread().isInterrupted()) {
                    log.info("task has interrupted. read finish. taskId={}", taskId);
                    return;
                }
                if (this.taskCancelCache.isCancel(taskId)) {
                    log.info("task had cancel. read finish. taskId={}", taskId);
                    return;
                }

                AssetCell document = cursor.next();
                batchCache.add(document);
            }

            // 刷新缓存，发送最后一批数据
            batchCache.flush();

        } catch (Exception e) {
            log.error("processBucket has ex", e);
            handleError(sessionContext, e);
        }

        // 等待所有异步发送完成
        long totalItems = batchCache.getAddTotal();
        synchronized (processedCount) {
            while (processedCount.get() < totalItems) {
                try {
                    processedCount.wait();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("【bucket-read-wait-interrupted】taskId:{}, bucketCode:{}", taskId, bucketCode);
                    break;
                }
            }
        }

        // 二次纠正记录总数，因为这查询数据过程中桶里面数据uts可能会变更，导致查询到的数据量不准确
        totalCounter.reportReal(bucketCode, count.get());
        traceBatch(batchSize.get(), taskId);
    }

    private void processDebug(MqSessionContext sessionContext, BucketReadDTO dto, String taskId, String bucketCode, TotalCounter totalCounter) {
        Query query = buildDebugQuery(dto);
        log.info("【bucket-read-query-debug】taskId:{}, bucketCode:{}, query:{}", taskId, bucketCode, query.getQueryObject());
        CacheMongoTemplate template = bucketCacheService.getClient(bucketCode);
        List<AssetCell> assetCells = template.find(query, AssetCell.class, bucketCode);
        AssetBucketStorageDTO bucketStorage = this.bucketCacheService.getBucket(bucketCode);

        try {
            if (CollectionUtils.isEmpty(assetCells)) {
                totalCounter.report(bucketCode, 0L);
                send(sessionContext, Collections.emptyList(), bucketStorage, null);
                log.info("【bucket-read-debug】taskId:{}, bucketCode:{}, no data", taskId, bucketCode);
                return;
            }
            totalCounter.report(bucketCode, (long) assetCells.size());
            send(sessionContext, assetCells, bucketStorage, null);
        } catch (Exception e) {
            log.error("【bucket-read-debug】 processDebug has ex", e);
            handleError(sessionContext, e);
        }
    }

    private void processBatch(MqSessionContext sessionContext, AssetTaskBatch taskBatch) {
        if (CollectionUtils.isEmpty(taskBatch.getDocIds())) {
            log.warn("【bucket-read-retry】 processBatch has no docIds. taskBatch={}", taskBatch.getBatchId());
            return;
        }
        String batchId = taskBatch.getBatchId();
        try {
            String bucketCode = BatchIdUtils.decodeBatchId(batchId);
            CacheMongoTemplate client = this.bucketCacheService.getClient(bucketCode);
            AssetBucketStorageDTO bucketStorage = this.bucketCacheService.getBucket(bucketCode);
            List<AssetCell> assetCells = client.find(new Query(Criteria.where(AssetCell.ID).in(taskBatch.getDocIds())), AssetCell.class, bucketCode);

            // 使用FixedCapacityCache进行批量发送
            processBatchWithCache(sessionContext, assetCells, bucketStorage, batchId);
        } catch (Exception e) {
            log.error("processBatch error", e);
            handleError(sessionContext, e);
        }
    }

    /**
     * 使用FixedCapacityCache处理批量数据发送
     */
    private void processBatchWithCache(MqSessionContext sessionContext, List<AssetCell> assetCells,
                                       AssetBucketStorageDTO bucketStorage, String batchId) throws InterruptedException {
        if (CollectionUtils.isEmpty(assetCells)) {
            log.debug("【bucket-read-retry-cache】no data to process for batchId: {}", batchId);
            return;
        }

        AtomicLong processedCount = new AtomicLong(0L);
        AtomicLong batchCount = new AtomicLong(0L);

        // 创建FixedCapacityCache，当缓存满时触发的回调
        Consumer<FixedCapacityCache<AssetCell>> cacheCallback = cache -> {
            try {
                // 避免并发修改异常，复制缓存内容
                List<AssetCell> dataToSend = cache.copy();
                batchCount.incrementAndGet();

                // 异步发送数据，对于重试场景，保持原有的batchId
                doSendAsync(sessionContext, dataToSend, bucketStorage, batchId, processedCount, dataToSend.size());

                log.debug("【bucket-read-retry-batch-sent】batchId:{}, batchCount:{}, dataSize:{}",
                        batchId, batchCount.get(), dataToSend.size());
            } catch (Exception e) {
                log.error("【bucket-read-retry-cache-callback】batchId:{}, error sending batch", batchId, e);
                handleError(sessionContext, e);
            }
        };

        // 使用配置的默认批次大小创建缓存
        FixedCapacityCache<AssetCell> batchCache = new FixedCapacityCache<>(bucketReadProperties.getDefaultBatch(), cacheCallback);

        // 将所有数据添加到缓存中
        for (AssetCell assetCell : assetCells) {
            batchCache.add(assetCell);
        }

        // 刷新缓存，发送最后一批数据
        batchCache.flush();

        // 等待所有异步发送完成
        long totalItems = batchCache.getAddTotal();
        synchronized (processedCount) {
            while (processedCount.get() < totalItems) {
                processedCount.wait();
            }
        }

        log.info("【bucket-read-retry-complete】batchId:{}, totalItems:{}, batchCount:{}",
                batchId, totalItems, batchCount.get());
    }

    // 异步发送total
    private void syncSendCount(CacheMongoTemplate template, String bucketCode, String taskId, Bson bson, TotalCounter totalCounter) {
        Thread.startVirtualThread(() -> {
            MDC.put(Fields.TASK_ID, taskId);
            try {
                BsonDocument doc = bson.toBsonDocument(Document.class, com.mongodb.MongoClientSettings.getDefaultCodecRegistry());
                Query query = new BasicQuery(doc.toJson());
                totalCounter.report(bucketCode, template.count(query, bucketCode));

            } catch (Exception exception) {
                log.error("send bucket total has ex:{}", exception.getMessage());
            } finally {
                MDC.clear();
            }
        });
    }


    /**
     * 构建参数
     *
     * @param bucketContext 桶信息
     * @return 查询条件
     */
    private Bson buildSyncStatusQuery(BucketReadDTO.BucketContext bucketContext) {
        List<Bson> filters = new ArrayList<>();
        // 只查询不通过的数据
        filters.add(Filters.eq(AssetCellProps.KEY_A, AssetAuditStatus.REJECT.getValue()));
        filters.addAll(buildUtsQuery(bucketContext));
        return Filters.and(filters);
    }

    // 构造查询条件
    private Bson buildQuery(BucketReadDTO.BucketContext bucketContext) {
        List<Bson> filters = new ArrayList<>();

        // 过滤调试数据
        filters.add(Filters.or(Filters.eq(AssetCellProps.KEY_DBG, AssetCellProps.BoolValue.FALSE.getValue()), Filters.exists(AssetCellProps.KEY_DBG, false)));

        // 过滤审核数据
        filters.add(Filters.or(Filters.eq(AssetCellProps.KEY_A, AssetAuditStatus.PASS.getValue()), Filters.eq(AssetCellProps.KEY_A, AssetAuditStatus.POST_INIT.getValue())));

        filters.addAll(buildUtsQuery(bucketContext));

        return Filters.and(filters);
    }

    // 调试条件
    private Query buildDebugQuery(BucketReadDTO dto) {
        // 存在ids则优先ids
        Query query = new Query();
        if (!CollectionUtils.isEmpty(dto.getIds())) {
            query.addCriteria(Criteria.where(AssetCell.ID).in(dto.getIds()));
            return query;
        }
        if (dto.getCellNum() != null) {
            query.limit(dto.getCellNum());
        }
        return query;
    }

    private List<Bson> buildUtsQuery(BucketReadDTO.BucketContext bucketContext) {
        List<Bson> filters = new ArrayList<>();
        if (bucketContext.getFrom() != null && bucketContext.getFrom() != 0) {
            // 防止从上次结束时间往前倒退1s开始查询，防止上次源桶数据读取过程中，uts在变化了，导致第二次读取漏掉了部分数据
            filters.add(Filters.gt(AssetCellProps.KEY_UPDATE_TS, bucketContext.getFrom() - 1000));
        }
        if (bucketContext.getTo() != null && bucketContext.getTo() != 0) {
            filters.add(Filters.lte(AssetCellProps.KEY_UPDATE_TS, bucketContext.getTo()));
        }
        return filters;
    }

    /**
     * 异步发送数据
     */
    private void doSendAsync(MqSessionContext sessionContext, List<AssetCell> list, AssetBucketStorageDTO bucketStorage,
                             String batchId, AtomicLong processedCount, int dataSize) {
        CompletableFuture.runAsync(() -> {
            try {
                doSend(sessionContext, list, bucketStorage, batchId);
            } catch (Exception e) {
                log.error("【bucket-read-async-send-error】error sending batch", e);
                handleError(sessionContext, e);
            } finally {
                // 通知处理完成
                synchronized (processedCount) {
                    processedCount.addAndGet(dataSize);
                    processedCount.notifyAll();
                }
            }
        });
    }

    // 仅重试会携带批次id
    private void send(MqSessionContext sessionContext, List<AssetCell> list, AssetBucketStorageDTO bucketStorage, String batchId) throws Exception {
        doSend(sessionContext, list, bucketStorage, batchId);
    }

    /**
     * 实际发送数据的方法
     */
    private void doSend(MqSessionContext sessionContext, List<AssetCell> list, AssetBucketStorageDTO bucketStorage, String batchId) throws Exception {
        // 携带批次头
        Map<String, String> headers = new HashMap<>();

        headers.put(PandoraConsts.PANDORA_BATCH_ID, batchId == null ? BatchIdUtils.generateBatchId(bucketStorage.getCode()) : batchId);
        headers.put(PandoraConsts.PANDORA_BATCH_COUNT, String.valueOf(list.size()));

        if (AssetKindCode.IDX.equals(bucketStorage.getKindCode())) {
            // 上线计划中索引库同步临时添加解压逻辑,[因索引库类桶mongo数据做了gzip压缩]
            list = list.stream().map(assetCell -> AssetCell.from(DocumentCompressor.getDefault().decompress(assetCell))).toList();
        }
        BucketReadResponse data = BucketReadResponse.builder().bucketCode(bucketStorage.getCode()).data(list).build();
        ApiResponse apiResponse = ApiResponseUtils.getApiResponse(ApiErrorCode.SUCCESS.getCode(), JSONObject.from(data));
        sessionContext.send(apiResponse, headers);
    }


    private void handleError(MqSessionContext sessionContext, Exception e) {
        try {
            sessionContext.send(ApiResponseUtils.getApiResponse(IndexingErrorType.READER_ERROR.getCode(), e.getMessage()));
        } catch (Exception ex) {
            log.error("【bucket-read-send-error】 failed to send error response", ex);
        }
    }


    /**
     * 汇报本次任务需要处理的数据单元总数
     */
    static class TotalCounter {
        private final String taskId;
        // 每个桶的初始总数
        private final Map<String, Long> bucketTotalMap = new HashMap<>();
        // 每个桶的实际发送的总数
        private final Map<String, Long> bucketRealTotalMap = new HashMap<>();
        private final PandoraApiRequestObserverBuilder apiRequestObserverBuilder;
        private final String ackUrl;

        public TotalCounter(String taskId, BucketReadDTO bucketReadDTO, String ackUrl, PandoraApiRequestObserverBuilder apiRequestObserverBuilder) {
            this.taskId = taskId;
            this.apiRequestObserverBuilder = apiRequestObserverBuilder;
            this.ackUrl = ackUrl;
            for (BucketReadDTO.BucketContext bucketContext : bucketReadDTO.getBucketCodeList()) {
                this.bucketTotalMap.put(bucketContext.getCode(), null);
                this.bucketRealTotalMap.put(bucketContext.getCode(), null);
            }
        }

        /**
         * 汇报每个桶的初始读取总数
         */
        public void report(String bucketCode, Long total) throws Exception {
            synchronized (this.bucketTotalMap) {
                this.bucketTotalMap.put(bucketCode, total);
                log.info("【bucket-read-total】taskId:{}, bucketCode:{}, total:{}", taskId, bucketCode, total);
                this.send(bucketTotalMap, "bucket-query-total");
            }
        }

        /**
         * 汇报每个桶的实际读取总数(二次纠正)
         */
        public void reportReal(String bucketCode, Long total) {
            synchronized (this.bucketRealTotalMap) {
                this.bucketRealTotalMap.put(bucketCode, total);
                log.info("【bucket-read-end】taskId:{}, bucketCode:{}, total:{}", taskId, bucketCode, total);
                try {
                    this.send(bucketRealTotalMap, "bucket-real-total");
                } catch (Exception e) {
                    log.error("【bucket-report-total-error】taskId:{}, bucketCode:{}, error:{}", taskId, bucketCode, e.getMessage(), e);
                }
            }
        }

        private void send(Map<String, Long> totalMap, String logMsg) throws Exception {
            // 每个桶是否都汇报了总数
            boolean allReported = totalMap.values().stream().allMatch(Objects::nonNull);
            if (!allReported) {
                return;
            }
            // 统计每个桶的总数之和
            long total = totalMap.values().stream()
                    .filter(Objects::nonNull)
                    .mapToLong(Long::longValue)
                    .sum();

            ApiRequestObserver apiRequestObserver = this.apiRequestObserverBuilder.build(this.ackUrl, new ApiResponseObserver() {
                @Override
                public void onReceive(ApiResponse response) {
                    log.info("【{}-ack】taskId:{}, response:{}", logMsg, taskId, response);
                }

                @Override
                public void onError(Throwable throwable) {
                    log.error("【{}-ack-error】taskId:{}, error:{}", logMsg, taskId, throwable.getMessage(), throwable);
                }

                @Override
                public void onCompleted() {
                    log.info("【{}-ack-completed】taskId:{}, total:{}", logMsg, taskId, total);
                }
            });
            ApiRequest apiRequest = new ApiRequest();
            apiRequest.setHeader(JSONObject.of(Fields.TASK_ID, taskId, "ctrl", "PROGRESS"));
            apiRequest.setPayload(JSONObject.of("total", total));
            apiRequestObserver.onSend(apiRequest, null);
            apiRequestObserver.onClose();
        }
    }

    private void traceBatch(long batchSize, String taskId) {
        try {
            JSONObject data = new JSONObject();
            data.put("batchSize", batchSize);
            traceUtils.info("BucketRead", data, Map.of("executionId", taskId));
        } catch (Exception e) {
            log.info("AssetTaskStateCallback#trace error: " + e.getMessage());
        }
    }
}
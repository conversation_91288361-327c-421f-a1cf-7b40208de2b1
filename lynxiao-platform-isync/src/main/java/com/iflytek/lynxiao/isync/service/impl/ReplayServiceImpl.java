package com.iflytek.lynxiao.isync.service.impl;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.thread.BlockPolicy;
import cn.hutool.core.thread.NamedThreadFactory;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListeningExecutorService;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.common.util.concurrent.RateLimiter;
import com.iflytek.lynxiao.common.component.FixedCapacityCache;
import com.iflytek.lynxiao.common.component.TaskMetricsReporter;
import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.data.constant.FeatureBaseTarget;
import com.iflytek.lynxiao.data.constant.Fields;
import com.iflytek.lynxiao.data.dto.featurebase.FeatureBaseDetailGetDTO;
import com.iflytek.lynxiao.data.dto.featurebase.FeatureBaseGetDTO;
import com.iflytek.lynxiao.data.message.index.IndexAck;
import com.iflytek.lynxiao.data.message.index.IndexAckEvent;
import com.iflytek.lynxiao.data.message.index.IndexData;
import com.iflytek.lynxiao.data.message.index.IndexDataOp;
import com.iflytek.lynxiao.data.message.index.request.DatasetSyncData;
import com.iflytek.lynxiao.data.utils.TaskUtil;
import com.iflytek.lynxiao.isync.config.IndexSyncProperties;
import com.iflytek.lynxiao.isync.consumer.TaskCtlManager;
import com.iflytek.lynxiao.isync.producer.IndexAckProducer;
import com.iflytek.lynxiao.isync.producer.IndexDataProducer;
import com.iflytek.lynxiao.isync.service.FeatureBaseService;
import com.iflytek.lynxiao.isync.service.ReplayService;
import com.iflytek.lynxiao.resource.domain.TaskFailDetail;
import com.iflytek.lynxiao.resource.repository.DocRepository;
import com.iflytek.lynxiao.resource.repository.TaskFailDetailRepository;
import com.mongodb.BasicDBObject;
import com.mongodb.client.MongoCursor;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.Tags;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.bson.Document;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import skynet.boot.exception.SkynetErrorCode;
import skynet.boot.mongo.DocumentCompressor;
import skynet.boot.pandora.api.ApiRequestGenerics;
import skynet.boot.pandora.api.ApiResponse;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/04/01
 */
@Service
@Slf4j
public class ReplayServiceImpl implements ReplayService {

    private final ExecutorService queryPool;
    private final ListeningExecutorService sendPool;

    private final FeatureBaseService featureBaseService;
    private final DocRepository docRepository;
    private final TaskFailDetailRepository taskFailDetailRepository;
    private final TaskCtlManager taskCtlManager;
    private final IndexDataProducer indexDataProducer;
    private final IndexSyncProperties indexSyncProperties;
    private final IndexAckProducer indexAckProducer;
    private final RateLimiter rateLimiter;
    private final TaskMetricsReporter taskMetricsReporter;
    private final MeterRegistry meterRegistry;

    public ReplayServiceImpl(FeatureBaseService featureBaseService,
                             DocRepository docRepository, TaskFailDetailRepository taskFailDetailRepository,
                             TaskCtlManager taskCtlManager,
                             IndexDataProducer indexDataProducer,
                             IndexSyncProperties indexSyncProperties,
                             IndexAckProducer indexAckProducer, TaskMetricsReporter taskMetricsReporter, MeterRegistry meterRegistry) {
        this.featureBaseService = featureBaseService;
        this.docRepository = docRepository;
        this.taskFailDetailRepository = taskFailDetailRepository;
        this.taskCtlManager = taskCtlManager;
        this.indexDataProducer = indexDataProducer;
        this.indexSyncProperties = indexSyncProperties;
        this.indexAckProducer = indexAckProducer;

        this.queryPool = new ThreadPoolExecutor(
                indexSyncProperties.getThreadPool().getCore(),
                indexSyncProperties.getThreadPool().getMax(),
                10,
                TimeUnit.MINUTES,
                new ArrayBlockingQueue<>(indexSyncProperties.getThreadPool().getSize()),
                new NamedThreadFactory("replay-pool", false),
                new ThreadPoolExecutor.AbortPolicy()
        );
        this.taskMetricsReporter = taskMetricsReporter;
        this.meterRegistry = meterRegistry;
        ExecutorService executorService4Send = new ThreadPoolExecutor(
                indexSyncProperties.getThreadPool().getCore(),
                indexSyncProperties.getThreadPool().getMax(),
                10,
                TimeUnit.MINUTES,
                new ArrayBlockingQueue<>(indexSyncProperties.getThreadPool().getSize()),
                new NamedThreadFactory("replay-send-pool", false),
                new BlockPolicy()
        );
        this.sendPool = MoreExecutors.listeningDecorator(executorService4Send);
        this.rateLimiter = (indexSyncProperties.getReplayQps() > 0) ? RateLimiter.create(indexSyncProperties.getReplayQps()) : null;
    }

    @Override
    public ApiResponse createTransferTask(ApiRequestGenerics<JSONObject, DatasetSyncData> task) {
        // 1.根据 srcIndexId 查询feature_base库，获取mongo库名称
        try {
            FeatureBaseDetailGetDTO featureBase = featureBaseService.getFeatureBase(task.getTraceId(),
                    (long) task.getPayload().getSrcIndexId());
            log.info("feature base query result: {}", featureBase);
            Assert.notNull(featureBase.getFeatureBaseConfig(), "featureBaseConfig in FeatureBaseGetDTO should not be null");

            this.queryPool.submit(() -> {
                try {
                    // mongoFullCollections暂时只支持size为1
                    String col = featureBase.getMongoFullCollections().getLast();
                    log.info("execute replay, collection name : {}", col);
                    if(!task.getPayload().isRetry()) {
                        // 返回携带total的ack，用于显示任务进度
                        this.sendTotalAck(col, task);
                    }
                    if(task.getPayload().isRetry()){
                        retry(col, task, featureBase);
                    } else {
                        dataQueryAndSend(col, task, featureBase);
                    }
                } catch (Exception e) {
                    log.error("create transferTask error", e);
                    this.sendErrAck(task, e);
                }
            });

        } catch (Exception e) {
            log.error("调用特征库异常", e);
            return new ApiResponse(task.getTraceId(), -1, e);
        }

        return new ApiResponse(task.getTraceId(), 0, "success");
    }

    private void sendTotalAck(String col, ApiRequestGenerics<JSONObject, DatasetSyncData> task) {
        DatasetSyncData data = task.getPayload();
        String query = StringUtils.isBlank(data.getQuery()) ? "{}" : data.getQuery();
        long total = this.docRepository.countDocs(col, query);
        log.info("task[{}], total count: {}", data.getTaskId(), total);
        IndexAck ack = new IndexAck();
        IndexAck.Header ackHeader = new IndexAck.Header();
        ackHeader.setEvent(IndexAckEvent.replay);
        ackHeader.setTraceId(task.getHeader().getString("traceId"));
        ackHeader.setTaskId(data.getTaskId());
        ackHeader.setCode(0);
        ackHeader.setContext("{}");
        ack.setHeader(ackHeader);
        IndexAck.Payload payload = new IndexAck.Payload();
        payload.setTotal(total);
        payload.setFail(0L);
        payload.setPass(0L);
        payload.setTaskId(data.getTaskId());
        ack.setPayload(payload);
        this.indexAckProducer.send(ack);
    }

    private void sendErrAck(ApiRequestGenerics<JSONObject, DatasetSyncData> task, Exception e) {
        DatasetSyncData data = task.getPayload();
        IndexAck ack = new IndexAck();
        IndexAck.Header ackHeader = new IndexAck.Header();
        ackHeader.setEvent(IndexAckEvent.replay);
        ackHeader.setTraceId(task.getHeader().getString("traceId"));
        ackHeader.setTaskId(data.getTaskId());
        ackHeader.setCode(-1);
        String msg = (e instanceof LynxiaoException) ? e.toString() : ExceptionUtil.stacktraceToString(e);
        ackHeader.setMessage(msg);
        ackHeader.setContext("{}");
        ack.setHeader(ackHeader);
        IndexAck.Payload payload = new IndexAck.Payload();
        payload.setTotal(0L);
        payload.setFail(0L);
        payload.setPass(0L);
        payload.setTaskId(data.getTaskId());
        ack.setPayload(payload);
        this.indexAckProducer.send(ack);
    }

    private void sendBatchFailAck(ApiRequestGenerics<JSONObject, DatasetSyncData> task, List<Long> ids, Exception e, String ctx){
        DatasetSyncData data = task.getPayload();
        IndexAck ack = new IndexAck();
        IndexAck.Header ackHeader = new IndexAck.Header();
        ackHeader.setOp(IndexDataOp.add);
        ackHeader.setEvent(IndexAckEvent.replay);
        ackHeader.setTraceId(task.getHeader().getString("traceId"));
        /*
         * 生成taskId,需要调整ack idx为1
         */
        Assert.hasText(data.getTaskId(), "taskId should not be blank");
        ackHeader.setTaskId(data.getTaskId());
        String msg = (e instanceof LynxiaoException) ? e.toString() : ExceptionUtil.stacktraceToString(e);
        int code = (e instanceof LynxiaoException) ? ((LynxiaoException) e).getCode() : SkynetErrorCode.ERROR.getCode();
        ackHeader.setCode(code);
        ackHeader.setMessage(msg);
        ackHeader.setContext(ctx);
        ack.setHeader(ackHeader);
        IndexAck.Payload payload = new IndexAck.Payload();
        payload.setErrorList(ids);
        payload.setTotal(null);
        payload.setFail((long)ids.size());
        payload.setPass((long)ids.size());
        payload.setTaskId(data.getTaskId());
        ack.setPayload(payload);
        this.indexAckProducer.send(ack);
    }

    private void dataQueryAndSend(String collectionName, ApiRequestGenerics<JSONObject, DatasetSyncData> task, FeatureBaseGetDTO featureBase) throws InterruptedException {
        DatasetSyncData payload = task.getPayload();
        AtomicLong done = new AtomicLong(0L);
        AtomicLong success = new AtomicLong(0L);
        // 缓存满时触发的回调
        Consumer<FixedCapacityCache<Document>> cacheCallback = cache -> {
            // 发送到下一个处理队列
            try {
                final StopWatch sw = StopWatch.createStarted();
                //避免并发修改异常
                List<Document> duplicateDocs = cache.copy();
                ListenableFuture<Integer> future = this.sendTopic(task, duplicateDocs, featureBase, "{}");
                future.addListener(() -> {
                    try {
                        success.addAndGet(future.get());
                    } catch (Exception e) {
                        log.error("exception in sending message", e);
                        this.sendBatchFailAck(task, duplicateDocs.stream().map(_doc -> _doc.getLong("_id")).toList(), e, "{}");
                    } finally {
                        this.taskMetricsReporter.reportTaskTime(reportKey(task.getPayload().getTaskId()), sw.getTime(TimeUnit.MILLISECONDS), duplicateDocs.size());
                        synchronized (done) {
                            done.addAndGet(duplicateDocs.size());
                            done.notifyAll();
                        }
                    }
                }, MoreExecutors.directExecutor());
            }catch (Exception e){
                log.error("", e);
                this.sendBatchFailAck(task, cache.stream().map(_doc -> _doc.getLong("_id")).toList(), e, "{}");
            }
        };
        FixedCapacityCache<Document> batchCache = new FixedCapacityCache<>(indexSyncProperties.getBatchSendSize(), cacheCallback);
        try (MongoCursor<Document> cursor = queryData(collectionName, payload)) {
            while (cursor.hasNext()) {
                if (this.taskCtlManager.isCancel(payload.getTaskId())) {
                    log.debug("task[{}] has been canceled. skip", payload.getTaskId());
                    break;
                }
                Document doc = cursor.next();
                batchCache.add(doc);
            }
            batchCache.flush();
        }
        long total = batchCache.getAddTotal();
        synchronized (done) {
            while(done.get() < total) {
                done.wait();
            }
        }
        log.info("collection name: {}, send total : {}, success: {}", collectionName, total, success.get());
    }

    private MongoCursor<Document> queryData(String featureBaseCollection, DatasetSyncData payload) {
        BasicDBObject queryObj = BasicDBObject.parse(payload.getQuery());
        return this.docRepository.findDocs(featureBaseCollection, queryObj);
    }

    private void retry(String collectionName, ApiRequestGenerics<JSONObject, DatasetSyncData> task, FeatureBaseGetDTO featureBase) throws InterruptedException {
        String taskId = task.getPayload().getTaskId();
        Assert.hasText(taskId, "taskId should not be blanks");
        taskId =  (taskId.contains(":")) ? TaskUtil.fromFormattedTaskId(taskId).getTaskId() : taskId;

        long total = 0;
        AtomicLong done = new AtomicLong(0L);
        AtomicLong success = new AtomicLong(0L);

        try (MongoCursor<Document> cursor = this.taskFailDetailRepository.findByTaskId(taskId)) {
            while(cursor.hasNext()){
                final StopWatch sw = StopWatch.createStarted();
                Document failDetailDoc = cursor.next();
                TaskFailDetail failDetail = JSON.to(TaskFailDetail.class, failDetailDoc);
                String ctx = new JSONObject().fluentPut(Fields.FAIL_DETAIL_ID, failDetailDoc.getObjectId("_id").toString()).toJSONString();
                Query query = new Query(Criteria.where("_id").in(failDetail.getIds()));
                if(log.isTraceEnabled()) {
                    log.trace("query {} : {}", collectionName, query.getQueryObject().toJson());
                }
                try {
                    List<Document> docs = docRepository.findDocListByCond(collectionName, query.getQueryObject());
                    int diff = failDetail.getIds().size() - docs.size();
                    if(diff != 0){
                        log.error("retry, {} docs not found in {}, taskId: {}, {} : ", diff, collectionName, taskId, ctx);
                        throw new LynxiaoException(SkynetErrorCode.ENTITY_NOT_FOUND.getCode(), "docs not found");
                    }
                    ListenableFuture<Integer> future = this.sendTopic(task, docs, featureBase, ctx);
                    total += docs.size();
                    future.addListener(() -> {
                        try {
                            success.addAndGet(future.get());
                        } catch (Exception e) {
                            log.error("exception in sending message", e);
                            this.sendBatchFailAck(task, docs.stream().map(_doc -> _doc.getLong("_id")).toList(), e, ctx);
                        } finally {
                            this.taskMetricsReporter.reportTaskTime(reportKey(task.getPayload().getTaskId()), sw.getTime(TimeUnit.MILLISECONDS), docs.size());
                            synchronized (done) {
                                done.addAndGet(docs.size());
                                done.notifyAll();
                            }
                        }
                    }, MoreExecutors.directExecutor());
                }catch (Exception e) {
                    log.error("exception in retry", e);
                    this.sendBatchFailAck(task, failDetail.getIds(), e, ctx);
                }
            }
        }
        synchronized (done) {
            while(done.get()< total){
                done.wait();
            }
        }
        log.info("replay retry: {}, send total : {}, success: {}", collectionName, total, success.get());
    }

    /**
     * 组装消息发送
     *
     * @param task           任务
     * @param docs           文档
     * @param featureBase    特征库
     * @return future
     */
    private ListenableFuture<Integer> sendTopic(ApiRequestGenerics<JSONObject, DatasetSyncData> task,
                                             List<Document> docs, FeatureBaseGetDTO featureBase, String ctx) {
        return this.sendPool.submit(() -> doSend(task, docs, featureBase, ctx));
    }

    private int doSend(ApiRequestGenerics<JSONObject, DatasetSyncData> task, List<Document> docs, FeatureBaseGetDTO featureBase, String ctx) {

        boolean success = true;
        StopWatch sw = StopWatch.createStarted();
        try {
            // 限流
            if(Objects.nonNull(this.rateLimiter)) {
                this.rateLimiter.acquire();
            }
            // 初始化payload
            List<Map<String, Object>> payload;
            if(this.indexSyncProperties.getDecompress()){
                DocumentCompressor documentCompressor = DocumentCompressor.getDefault();
                payload = docs.stream().map(documentCompressor::decompress).toList();
            }else{
                payload = new ArrayList<>(docs);
            }
            // 初始化header
            DatasetSyncData taskPayload = task.getPayload();
            IndexData.Header header = new IndexData.Header();
            header.setTraceId(task.getTraceId());
            header.setTs(System.currentTimeMillis());
            header.setOp(IndexDataOp.add);
            header.setHot(false);
            header.setTaskId(taskPayload.getTaskId());
            header.setDataCode("");
            header.setIndexCode(taskPayload.getIndexCode());
            header.setIndexId((long) taskPayload.getDestIndexId());
            header.setRegion(taskPayload.getDestRegion());
            header.setContext(ctx);
            header.setFeatureBaseConfig(featureBase.getFeatureBaseConfig());
            List<String> esIncludeFields = featureBase.getFeatureBaseConfig().parseEsIncludeFields();
            header.setEsIncludeFields(esIncludeFields);
            List<FeatureBaseTarget> target = taskPayload.getTarget().stream().map(FeatureBaseTarget::valueOf).collect(Collectors.toList());
            header.setTarget(target);
            // 构造IndexData
            IndexData indexData = new IndexData();
            indexData.setHeader(header);
            indexData.setPayload(payload);
            log.debug("send replay data , feature base: {}, docs: {}", featureBase.getId(), indexData.getPayload().size());
            indexDataProducer.send(indexData, taskPayload.getDestRegion());
            return payload.size();
        } catch (Exception e) {
            success = false;
            throw e;
        } finally {
            Tags tags = Tags.of(Tag.of("success", String.valueOf(success)), Tag.of("dest", task.getPayload().getDestRegion()));
            this.meterRegistry.timer("lynxiao.isync.replay.cost.time", tags).record(sw.getDuration());
            this.meterRegistry.counter("lynxiao.isync.replay.total", tags).increment(docs.size());
        }

    }

    private static String reportKey(String taskId) {
        return String.format("replay:%s", taskId);
    }
}
